# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class Accountheads(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.Char<PERSON>ield(db_column='Description', max_length=500)  # Field name made lowercase.
    category = models.Char<PERSON>ield(db_column='Category', max_length=500, blank=True, null=True)  # Field name made lowercase.
    country = models.CharField(db_column='Country', max_length=200, blank=True, null=True)  # Field name made lowercase.
    state = models.CharField(db_column='State', max_length=200, blank=True, null=True)  # Field name made lowercase.
    city = models.Char<PERSON>ield(db_column='City', max_length=200, blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=2000, blank=True, null=True)  # Field name made lowercase.
    phone = models.CharField(db_column='Phone', max_length=50, blank=True, null=True)  # Field name made lowercase.
    mobile = models.CharField(db_column='Mobile', max_length=50, blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fax = models.CharField(db_column='Fax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    systemused = models.CharField(db_column='SystemUsed', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'accountheads'


class Addressbook(models.Model):
    id = models.IntegerField(primary_key=True)
    category = models.ForeignKey('Managelist', models.DO_NOTHING, db_column='Category', blank=True, null=True)  # Field name made lowercase.
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    contact_person = models.CharField(db_column='Contact Person', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)  # Field name made lowercase.
    phone1 = models.CharField(db_column='Phone1', max_length=50, blank=True, null=True)  # Field name made lowercase.
    phone2 = models.CharField(db_column='Phone2', max_length=50, blank=True, null=True)  # Field name made lowercase.
    mobile_phone = models.CharField(db_column='Mobile Phone', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    city = models.CharField(db_column='City', max_length=200, blank=True, null=True)  # Field name made lowercase.
    state = models.CharField(db_column='State', max_length=200, blank=True, null=True)  # Field name made lowercase.
    zip = models.CharField(db_column='Zip', max_length=50, blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=200, blank=True, null=True)  # Field name made lowercase.
    fax = models.CharField(db_column='Fax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    comment = models.TextField(db_column='Comment', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'addressbook'


class Allowance(models.Model):
    id = models.IntegerField(primary_key=True)
    allowance = models.ForeignKey('Managelist', models.DO_NOTHING, db_column='allowance', blank=True, null=True)
    amount = models.CharField(max_length=50, blank=True, null=True)
    staffid = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='staffID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'allowance'


class Appointments(models.Model):
    appointmentid = models.IntegerField(db_column='AppointmentID', primary_key=True)  # Field name made lowercase.
    appointment_date = models.DateTimeField(db_column='Appointment Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    appointment_time_from = models.CharField(db_column='Appointment Time From', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    appointment_time_to = models.CharField(db_column='Appointment Time To', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    visitor_name = models.CharField(db_column='Visitor Name', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    person_to_visit = models.IntegerField(db_column='Person To Visit', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    notes = models.CharField(db_column='Notes', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    mobile = models.CharField(db_column='Mobile', max_length=50, blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'appointments'


class Appointmenttimeslot(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    time_from = models.CharField(db_column='Time From', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    time_to = models.CharField(db_column='Time To', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.

    class Meta:
        managed = False
        db_table = 'appointmenttimeslot'


class Bill(models.Model):
    bill_no = models.IntegerField(db_column='Bill No', primary_key=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_date = models.DateTimeField(db_column='Bill Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_time = models.CharField(db_column='Bill Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    billing_category = models.CharField(db_column='Billing Category', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.
    final_bill = models.DecimalField(db_column='Final Bill', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_status = models.CharField(db_column='Bill Status', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_balance = models.DecimalField(db_column='Bill Balance', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    total_charge = models.DecimalField(db_column='Total Charge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    tax_amount = models.DecimalField(db_column='Tax Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount_amount = models.DecimalField(db_column='Discount Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    stayid = models.ForeignKey('Stays', models.DO_NOTHING, db_column='stayID', blank=True, null=True)  # Field name made lowercase.
    billedby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='BilledBy', blank=True, null=True)  # Field name made lowercase.
    paidamount = models.DecimalField(db_column='PaidAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    clientname = models.CharField(db_column='ClientName', max_length=500, blank=True, null=True)  # Field name made lowercase.
    accountheadid = models.ForeignKey(Accountheads, models.DO_NOTHING, db_column='AccountHeadID', blank=True, null=True)  # Field name made lowercase.
    trialid = models.ForeignKey('Trialbalance', models.DO_NOTHING, db_column='trialID', blank=True, null=True)  # Field name made lowercase.
    viewstatus = models.CharField(db_column='viewStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.
    sendstatus = models.CharField(db_column='sendStatus', max_length=200, blank=True, null=True)  # Field name made lowercase.
    servingstaff = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='servingStaff', related_name='bill_servingstaff_set', blank=True, null=True)  # Field name made lowercase.
    departmentid = models.ForeignKey('Departments', models.DO_NOTHING, db_column='departmentID', blank=True, null=True)  # Field name made lowercase.
    posstatus = models.CharField(db_column='posStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'bill'


class Cancelreservation(models.Model):
    id = models.IntegerField(primary_key=True)
    reservationcode = models.ForeignKey('Stays', models.DO_NOTHING, db_column='ReservationCode', blank=True, null=True)  # Field name made lowercase.
    reason = models.CharField(db_column='Reason', max_length=1000, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'cancelreservation'


class Chargecategories(models.Model):
    categoryid = models.IntegerField(db_column='CategoryID', primary_key=True)  # Field name made lowercase.
    category_description = models.CharField(db_column='Category Description', max_length=200)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    type = models.CharField(db_column='Type', max_length=200, blank=True, null=True)  # Field name made lowercase.
    hotesettingid = models.ForeignKey('Hotelsettings', models.DO_NOTHING, db_column='hoteSettingID', blank=True, null=True)  # Field name made lowercase.
    systemused = models.CharField(db_column='systemUsed', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'chargecategories'


class Chargemanager(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    charge_type = models.CharField(db_column='Charge Type', max_length=100)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    charge_category = models.ForeignKey(Chargecategories, models.DO_NOTHING, db_column='Charge Category')  # Field name made lowercase. Field renamed to remove unsuitable characters.
    description = models.CharField(db_column='Description', max_length=500)  # Field name made lowercase.
    standard_charge = models.DecimalField(db_column='Standard Charge', max_digits=19, decimal_places=4)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    chargecode = models.CharField(db_column='ChargeCode', max_length=50, blank=True, null=True)  # Field name made lowercase.
    inventoryid = models.IntegerField(db_column='InventoryID', blank=True, null=True)  # Field name made lowercase.
    active = models.CharField(db_column='Active', max_length=50, blank=True, null=True)  # Field name made lowercase.
    stockqty = models.IntegerField(db_column='stockQty', blank=True, null=True)  # Field name made lowercase.
    reorderlevel = models.IntegerField(db_column='ReOrderLevel', blank=True, null=True)  # Field name made lowercase.
    purchaseprice = models.DecimalField(db_column='PurchasePrice', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    barcode = models.TextField(blank=True, null=True)
    manufacturerbarcode = models.CharField(max_length=50, blank=True, null=True)
    profit = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    itemimage = models.TextField(db_column='ItemImage', blank=True, null=True)  # Field name made lowercase.
    foodcostpercentage = models.BigIntegerField(db_column='foodCostPercentage', blank=True, null=True)  # Field name made lowercase.
    itemstatus = models.CharField(db_column='itemStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'chargemanager'


class Charges(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    chargecode = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='ChargeCode', blank=True, null=True)  # Field name made lowercase.
    charge_date = models.DateTimeField(db_column='Charge Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    charge_time = models.CharField(db_column='Charge Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    unit_charge = models.DecimalField(db_column='Unit Charge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    unit = models.IntegerField(db_column='Unit', blank=True, null=True)  # Field name made lowercase.
    total = models.DecimalField(db_column='Total', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    final_payable = models.DecimalField(db_column='Final Payable', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_no = models.ForeignKey(Bill, models.DO_NOTHING, db_column='Bill No', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    taxamount = models.DecimalField(db_column='TaxAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountamount = models.DecimalField(db_column='DiscountAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    chargeby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='chargeBy', blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    dateto = models.DateTimeField(db_column='dateTo', blank=True, null=True)  # Field name made lowercase.
    purchaseprice = models.DecimalField(db_column='purchasePrice', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    finalpurchaseprice = models.DecimalField(db_column='FinalPurchasePrice', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    profit = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    departmentid = models.ForeignKey('Departments', models.DO_NOTHING, db_column='DepartmentID', blank=True, null=True)  # Field name made lowercase.
    posstatus = models.CharField(db_column='posStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.
    manaulchargereceiptno = models.IntegerField(db_column='manaulChargeReceiptNo', blank=True, null=True)  # Field name made lowercase.
    description = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'charges'


class Chargesitemcosting(models.Model):
    id = models.IntegerField(primary_key=True)
    ingredientid = models.IntegerField(db_column='ingredientID', blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(max_length=50, blank=True, null=True)
    chargeid = models.IntegerField(db_column='chargeID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'chargesitemcosting'


class Clentaccount(models.Model):
    id = models.IntegerField(primary_key=True)
    clientid = models.IntegerField(db_column='ClientID', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    amount = models.CharField(db_column='Amount', max_length=50, blank=True, null=True)  # Field name made lowercase.
    action = models.IntegerField(db_column='Action', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'clentaccount'


class Clients(models.Model):
    id = models.IntegerField(primary_key=True)
    firstname = models.CharField(db_column='FirstName', max_length=200, blank=True, null=True)  # Field name made lowercase.
    lastname = models.CharField(db_column='LastName', max_length=200, blank=True, null=True)  # Field name made lowercase.
    sex = models.CharField(db_column='Sex', max_length=50, blank=True, null=True)  # Field name made lowercase.
    phone = models.CharField(db_column='Phone', max_length=50, blank=True, null=True)  # Field name made lowercase.
    mobile = models.CharField(db_column='Mobile', max_length=50, blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)  # Field name made lowercase.
    jobtitle = models.CharField(db_column='JobTitle', max_length=200, blank=True, null=True)  # Field name made lowercase.
    city = models.CharField(db_column='City', max_length=200, blank=True, null=True)  # Field name made lowercase.
    state = models.CharField(db_column='State', max_length=200, blank=True, null=True)  # Field name made lowercase.
    zip = models.CharField(db_column='Zip', max_length=50, blank=True, null=True)  # Field name made lowercase.
    country = models.CharField(db_column='Country', max_length=200, blank=True, null=True)  # Field name made lowercase.
    fax = models.CharField(db_column='Fax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=50, blank=True, null=True)  # Field name made lowercase.
    company = models.CharField(db_column='Company', max_length=500, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fullname = models.CharField(db_column='FullName', max_length=500, blank=True, null=True)  # Field name made lowercase.
    clienttype = models.CharField(max_length=50, blank=True, null=True)
    companycarrier = models.ForeignKey('Company', models.DO_NOTHING, db_column='companycarrier', blank=True, null=True)
    barcode = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'clients'


class Codegenerationsetting(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    seperation = models.CharField(db_column='Seperation', max_length=50, blank=True, null=True)  # Field name made lowercase.
    max_length = models.CharField(db_column='Max Length', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    start_number = models.CharField(db_column='Start Number', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    prefix = models.CharField(db_column='Prefix', max_length=50, blank=True, null=True)  # Field name made lowercase.
    calender_year = models.CharField(db_column='Calender Year', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    category = models.CharField(max_length=50, blank=True, null=True)
    increment = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'codegenerationsetting'


class Commandmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    menu = models.CharField(db_column='Menu', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'commandmanager'


class Company(models.Model):
    id = models.IntegerField(primary_key=True)
    companyname = models.CharField(max_length=500, blank=True, null=True)
    phone1 = models.CharField(max_length=50, blank=True, null=True)
    phone2 = models.CharField(max_length=50, blank=True, null=True)
    mobile1 = models.CharField(max_length=50, blank=True, null=True)
    mobile2 = models.CharField(max_length=50, blank=True, null=True)
    email = models.CharField(max_length=100, blank=True, null=True)
    zip = models.CharField(max_length=50, blank=True, null=True)
    fax = models.CharField(max_length=200, blank=True, null=True)
    city = models.CharField(max_length=200, blank=True, null=True)
    state = models.CharField(max_length=200, blank=True, null=True)
    country = models.CharField(max_length=200, blank=True, null=True)
    address = models.CharField(max_length=1000, blank=True, null=True)
    companycode = models.CharField(max_length=50, blank=True, null=True)
    systemuse = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'company'


class Companysettings(models.Model):
    id = models.IntegerField(primary_key=True)
    companyname = models.CharField(db_column='CompanyName', max_length=500, blank=True, null=True)  # Field name made lowercase.
    companylogo = models.TextField(db_column='CompanyLogo', blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=1000, blank=True, null=True)  # Field name made lowercase.
    city = models.CharField(db_column='City', max_length=200, blank=True, null=True)  # Field name made lowercase.
    state = models.CharField(db_column='State', max_length=200, blank=True, null=True)  # Field name made lowercase.
    zip = models.CharField(db_column='Zip', max_length=200, blank=True, null=True)  # Field name made lowercase.
    country = models.CharField(db_column='Country', max_length=200, blank=True, null=True)  # Field name made lowercase.
    phone = models.CharField(db_column='Phone', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fax = models.CharField(db_column='Fax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    currencysign = models.CharField(db_column='CurrencySign', max_length=50, blank=True, null=True)  # Field name made lowercase.
    currencyalign = models.CharField(db_column='CurrencyAlign', max_length=50, blank=True, null=True)  # Field name made lowercase.
    currencyformat = models.CharField(db_column='CurrencyFormat', max_length=50, blank=True, null=True)  # Field name made lowercase.
    currencydenomination = models.CharField(db_column='CurrencyDenomination', max_length=500, blank=True, null=True)  # Field name made lowercase.
    secondcurrencysign = models.CharField(db_column='SecondCurrencySign', max_length=500, blank=True, null=True)  # Field name made lowercase.
    secondcurrencyrate = models.CharField(db_column='SecondCurrencyRate', max_length=500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'companysettings'


class Customerservices(models.Model):
    id = models.IntegerField(primary_key=True)
    service_description = models.CharField(db_column='service description', max_length=50, blank=True, null=True)  # Field renamed to remove unsuitable characters.
    amount = models.DecimalField(db_column='Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    chargeid = models.IntegerField(db_column='chargeID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'customerservices'


class Deletedbill(models.Model):
    bill_no = models.IntegerField(db_column='Bill No', primary_key=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_date = models.DateTimeField(db_column='Bill Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_time = models.CharField(db_column='Bill Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    billing_category = models.CharField(db_column='Billing Category', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.
    final_bill = models.DecimalField(db_column='Final Bill', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_status = models.CharField(db_column='Bill Status', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_balance = models.DecimalField(db_column='Bill Balance', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    total_charge = models.DecimalField(db_column='Total Charge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    tax_amount = models.DecimalField(db_column='Tax Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount_amount = models.DecimalField(db_column='Discount Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    stayid = models.IntegerField(db_column='stayID', blank=True, null=True)  # Field name made lowercase.
    billedby = models.IntegerField(db_column='BilledBy', blank=True, null=True)  # Field name made lowercase.
    paidamount = models.DecimalField(db_column='PaidAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    clientname = models.CharField(db_column='ClientName', max_length=500, blank=True, null=True)  # Field name made lowercase.
    accountheadid = models.IntegerField(db_column='AccountHeadID', blank=True, null=True)  # Field name made lowercase.
    trialid = models.IntegerField(db_column='trialID', blank=True, null=True)  # Field name made lowercase.
    viewstatus = models.CharField(db_column='viewStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'deletedbill'


class Deletedcharges(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    chargecode = models.IntegerField(db_column='ChargeCode', blank=True, null=True)  # Field name made lowercase.
    charge_date = models.DateTimeField(db_column='Charge Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    charge_time = models.CharField(db_column='Charge Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    unit_charge = models.DecimalField(db_column='Unit Charge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    unit = models.IntegerField(db_column='Unit', blank=True, null=True)  # Field name made lowercase.
    total = models.DecimalField(db_column='Total', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    final_payable = models.DecimalField(db_column='Final Payable', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    bill_no = models.IntegerField(db_column='Bill No', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    taxamount = models.DecimalField(db_column='TaxAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountamount = models.DecimalField(db_column='DiscountAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    chargeby = models.IntegerField(db_column='chargeBy', blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'deletedcharges'


class Departmentitems(models.Model):
    id = models.IntegerField(primary_key=True)
    departmentid = models.ForeignKey('Departments', models.DO_NOTHING, db_column='departmentID', blank=True, null=True)  # Field name made lowercase.
    itemid = models.ForeignKey('Iteminventory', models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    stockqty = models.BigIntegerField(db_column='stockQty', blank=True, null=True)  # Field name made lowercase.
    portionstockqty = models.IntegerField(db_column='PortionStockQty', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'departmentitems'


class Departments(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=50)
    comments = models.CharField(max_length=1000, blank=True, null=True)
    type = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'departments'


class Discounttypes(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    discount = models.CharField(db_column='Discount', max_length=50, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=1500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'discounttypes'


class Expenses(models.Model):
    expenseid = models.IntegerField(db_column='ExpenseID', primary_key=True)  # Field name made lowercase.
    expense_date = models.DateTimeField(db_column='Expense Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    expensetime = models.CharField(db_column='ExpenseTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    amount = models.DecimalField(db_column='Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    paid_to = models.ForeignKey(Accountheads, models.DO_NOTHING, db_column='Paid To', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=2000, blank=True, null=True)  # Field name made lowercase.
    created_by = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='Created By', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    vendor = models.ForeignKey('Vendors', models.DO_NOTHING, db_column='vendor', blank=True, null=True)
    cashbank = models.ForeignKey(Accountheads, models.DO_NOTHING, db_column='Cashbank', related_name='expenses_cashbank_set', blank=True, null=True)  # Field name made lowercase.
    voucherno = models.CharField(max_length=50, blank=True, null=True)
    expensetype = models.CharField(db_column='ExpenseType', max_length=100, blank=True, null=True)  # Field name made lowercase.
    trialid = models.ForeignKey('Trialbalance', models.DO_NOTHING, db_column='trialID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'expenses'


class Feeschedule(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    charge_code = models.IntegerField(db_column='Charge Code')  # Field name made lowercase. Field renamed to remove unsuitable characters.
    schedule = models.CharField(db_column='Schedule', max_length=200)  # Field name made lowercase.
    charge = models.DecimalField(db_column='Charge', max_digits=19, decimal_places=4)  # Field name made lowercase.
    charge_type = models.CharField(db_column='Charge Type', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.

    class Meta:
        managed = False
        db_table = 'feeschedule'


class Feeschedules(models.Model):
    id = models.IntegerField(primary_key=True)
    itemid = models.ForeignKey('Items', models.DO_NOTHING, db_column='ItemID', blank=True, null=True)  # Field name made lowercase.
    schedule = models.CharField(db_column='Schedule', max_length=200, blank=True, null=True)  # Field name made lowercase.
    price = models.CharField(db_column='Price', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'feeschedules'


class Fettytesttable(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    category = models.CharField(max_length=50, blank=True, null=True)
    delid = models.IntegerField(db_column='delID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'fettytesttable'


class Finishmaterialwastelog(models.Model):
    id = models.IntegerField(primary_key=True)
    chargecode = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='ChargeCode', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    unitcost = models.DecimalField(db_column='unitCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    qty = models.IntegerField(blank=True, null=True)
    total = models.DecimalField(db_column='Total', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    finishwastemanagerid = models.ForeignKey('Finishmaterialwastelogmanager', models.DO_NOTHING, db_column='FinishWasteManagerID', blank=True, null=True)  # Field name made lowercase.
    departmentid = models.ForeignKey(Departments, models.DO_NOTHING, db_column='DepartmentID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'finishmaterialwastelog'


class Finishmaterialwastelogmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    purchasecategory = models.CharField(db_column='purchaseCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    wasteno = models.CharField(db_column='wasteNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    staffresponsible = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='staffResponsible', blank=True, null=True)  # Field name made lowercase.
    department = models.ForeignKey(Departments, models.DO_NOTHING, db_column='department', blank=True, null=True)
    totalcost = models.DecimalField(db_column='totalCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    wastereason = models.CharField(db_column='wasteReason', max_length=2000, blank=True, null=True)  # Field name made lowercase.
    createdby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='createdBy', related_name='finishmaterialwastelogmanager_createdby_set', blank=True, null=True)  # Field name made lowercase.
    trialid = models.ForeignKey('Trialbalance', models.DO_NOTHING, db_column='trialID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'finishmaterialwastelogmanager'


class Formcommands(models.Model):
    id = models.IntegerField(primary_key=True)
    userid = models.ForeignKey('Users', models.DO_NOTHING, db_column='UserID', blank=True, null=True)  # Field name made lowercase.
    menu = models.CharField(db_column='Menu', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.IntegerField(db_column='Status', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'formcommands'


class Hotelsettings(models.Model):
    code = models.IntegerField(primary_key=True)
    hotel_name = models.CharField(db_column='Hotel Name', max_length=500)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    email_adress = models.CharField(db_column='Email Adress', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    website = models.CharField(db_column='Website', max_length=50, blank=True, null=True)  # Field name made lowercase.
    adress = models.CharField(db_column='Adress', max_length=500, blank=True, null=True)  # Field name made lowercase.
    phone_number1 = models.CharField(db_column='Phone Number1', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    phone_number2 = models.CharField(db_column='Phone Number2', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    logo = models.TextField(db_column='Logo', blank=True, null=True)  # Field name made lowercase.
    currency = models.CharField(db_column='Currency', max_length=100, blank=True, null=True)  # Field name made lowercase.
    currency_symbol = models.CharField(db_column='Currency Symbol', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    halfdaypercent = models.CharField(db_column='HalfDayPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    defaulttax = models.CharField(db_column='defaultTax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    defaultpension = models.CharField(db_column='defaultPension', max_length=50, blank=True, null=True)  # Field name made lowercase.
    nationalinssurance = models.CharField(db_column='NationalInssurance', max_length=50, blank=True, null=True)  # Field name made lowercase.
    hotelid = models.CharField(db_column='HotelID', max_length=50, blank=True, null=True)  # Field name made lowercase.
    softwareaccess = models.CharField(db_column='SoftwareAccess', max_length=50, blank=True, null=True)  # Field name made lowercase.
    servername = models.CharField(db_column='serverName', max_length=50, blank=True, null=True)  # Field name made lowercase.
    databasename = models.CharField(db_column='DatabaseName', max_length=50, blank=True, null=True)  # Field name made lowercase.
    userid = models.CharField(db_column='userID', max_length=50, blank=True, null=True)  # Field name made lowercase.
    password = models.CharField(max_length=50, blank=True, null=True)
    posprintertype = models.CharField(db_column='PosPrinterType', max_length=50, blank=True, null=True)  # Field name made lowercase.
    printernumbersofcopies = models.IntegerField(db_column='printerNumbersOfCopies', blank=True, null=True)  # Field name made lowercase.
    lockaddress = models.CharField(db_column='lockAddress', max_length=500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'hotelsettings'


class Ingredients(models.Model):
    id = models.IntegerField(primary_key=True)
    itemid = models.IntegerField(db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    descriptionid = models.IntegerField(db_column='DescriptionID', blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ingredients'


class Inventorycategory(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'inventorycategory'


class Inventoryunits(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'inventoryunits'


class Iteminventory(models.Model):
    id = models.IntegerField(primary_key=True)
    itemtype = models.CharField(db_column='ItemType', max_length=50, blank=True, null=True)  # Field name made lowercase.
    itemcategory = models.ForeignKey(Chargecategories, models.DO_NOTHING, db_column='ItemCategory', blank=True, null=True)  # Field name made lowercase.
    item = models.CharField(max_length=500, blank=True, null=True)
    barcode = models.CharField(max_length=50, blank=True, null=True)
    measurementunit = models.ForeignKey('Managelist', models.DO_NOTHING, db_column='measurementUnit', blank=True, null=True)  # Field name made lowercase.
    unitprice = models.DecimalField(db_column='unitPrice', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    stockavailable = models.BigIntegerField(db_column='stockAvailable', blank=True, null=True)  # Field name made lowercase.
    reorderlevel = models.BigIntegerField(db_column='reOrderLevel', blank=True, null=True)  # Field name made lowercase.
    expirydate = models.DateTimeField(db_column='ExpiryDate', blank=True, null=True)  # Field name made lowercase.
    tax = models.CharField(max_length=50, blank=True, null=True)
    notes = models.CharField(max_length=1000, blank=True, null=True)
    sku = models.CharField(max_length=50, blank=True, null=True)
    storage = models.ForeignKey('Managelist', models.DO_NOTHING, db_column='storage', related_name='iteminventory_storage_set', blank=True, null=True)
    portionstockavailable = models.BigIntegerField(db_column='PortionStockAvailable')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'iteminventory'


class Itemordered(models.Model):
    id = models.IntegerField(primary_key=True)
    orderid = models.IntegerField(db_column='OrderID', blank=True, null=True)  # Field name made lowercase.
    description = models.IntegerField(db_column='Description', blank=True, null=True)  # Field name made lowercase.
    price = models.CharField(db_column='Price', max_length=50, blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ordertype = models.CharField(db_column='OrderType', max_length=50, blank=True, null=True)  # Field name made lowercase.
    parentid = models.IntegerField(db_column='ParentID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'itemordered'


class Itemrequisitionmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    date = models.DateField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    purchasecategory = models.CharField(db_column='purchaseCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=1000, blank=True, null=True)  # Field name made lowercase.
    total = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    department = models.ForeignKey('Managelist', models.DO_NOTHING, db_column='department', blank=True, null=True)
    requestedby = models.IntegerField(db_column='requestedBy', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'itemrequisitionmanager'


class Items(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    price = models.CharField(db_column='Price', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=50, blank=True, null=True)  # Field name made lowercase.
    barcode = models.CharField(db_column='Barcode', max_length=50, blank=True, null=True)  # Field name made lowercase.
    category = models.IntegerField(db_column='Category', blank=True, null=True)  # Field name made lowercase.
    modifiercategory = models.IntegerField(db_column='ModifierCategory', blank=True, null=True)  # Field name made lowercase.
    note = models.CharField(db_column='Note', max_length=500, blank=True, null=True)  # Field name made lowercase.
    vatid = models.CharField(db_column='VATID', max_length=50, blank=True, null=True)  # Field name made lowercase.
    alternativename = models.CharField(db_column='AlternativeName', max_length=50, blank=True, null=True)  # Field name made lowercase.
    icon = models.TextField(db_column='Icon', blank=True, null=True)  # Field name made lowercase.
    issalesitem = models.CharField(db_column='IsSalesItem', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ispurchaseitem = models.CharField(db_column='IsPurchaseItem', max_length=50, blank=True, null=True)  # Field name made lowercase.
    isstockitem = models.CharField(db_column='IsStockItem', max_length=50, blank=True, null=True)  # Field name made lowercase.
    inventorycategory = models.IntegerField(db_column='InventoryCategory', blank=True, null=True)  # Field name made lowercase.
    inventoryunit = models.IntegerField(db_column='InventoryUnit', blank=True, null=True)  # Field name made lowercase.
    returntostockonrefund = models.CharField(db_column='ReturnToStockOnRefund', max_length=50, blank=True, null=True)  # Field name made lowercase.
    usepriceschedule = models.CharField(db_column='UsePriceSchedule', max_length=50, blank=True, null=True)  # Field name made lowercase.
    tax = models.ForeignKey('Taxation', models.DO_NOTHING, db_column='Tax', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'items'


class Loanadvancepayment(models.Model):
    id = models.IntegerField(primary_key=True)
    employeid = models.IntegerField(db_column='EmployeID')  # Field name made lowercase.
    occupation = models.CharField(db_column='Occupation', max_length=200)  # Field name made lowercase.
    type = models.CharField(db_column='Type', max_length=100)  # Field name made lowercase.
    date = models.CharField(db_column='Date', max_length=50)  # Field name made lowercase.
    cash_bank = models.CharField(db_column='Cash/Bank', max_length=200)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    narration = models.CharField(db_column='Narration', max_length=1000)  # Field name made lowercase.
    amount = models.DecimalField(db_column='Amount', max_digits=19, decimal_places=4)  # Field name made lowercase.
    paidby = models.IntegerField(db_column='PaidBy', blank=True, null=True)  # Field name made lowercase.
    time = models.CharField(db_column='Time', max_length=50, blank=True, null=True)  # Field name made lowercase.
    loanadvancestatus = models.CharField(db_column='LoanAdvanceStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'loanadvancepayment'


class Locations(models.Model):
    id = models.IntegerField(db_column='ID', primary_key=True)  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=50, blank=True, null=True)  # Field name made lowercase.
    locationtype = models.CharField(db_column='LocationType', max_length=50, blank=True, null=True)  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=50, blank=True, null=True)  # Field name made lowercase.
    department = models.IntegerField(db_column='Department', blank=True, null=True)  # Field name made lowercase.
    feeschedule = models.IntegerField(db_column='FeeSchedule', blank=True, null=True)  # Field name made lowercase.
    floorid = models.IntegerField(db_column='FloorID', blank=True, null=True)  # Field name made lowercase.
    chargeid = models.IntegerField(db_column='ChargeID', blank=True, null=True)  # Field name made lowercase.
    wardid = models.IntegerField(db_column='WardID', blank=True, null=True)  # Field name made lowercase.
    currentstatus = models.CharField(db_column='CurrentStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.
    nursingstation = models.IntegerField(db_column='NursingStation', blank=True, null=True)  # Field name made lowercase.
    locationdetails = models.CharField(db_column='LocationDetails', max_length=500, blank=True, null=True)  # Field name made lowercase.
    roomtype = models.CharField(db_column='RoomType', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'locations'


class Managelist(models.Model):
    code_id = models.IntegerField(db_column='Code/ID', primary_key=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    definition = models.CharField(db_column='Definition', max_length=200, blank=True, null=True)  # Field name made lowercase.
    choices_drop_downs = models.CharField(db_column='Choices/Drop Downs', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    system_option = models.CharField(db_column='System Option', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.

    class Meta:
        managed = False
        db_table = 'managelist'


class Managelistcategory(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    category = models.CharField(db_column='Category', max_length=200, blank=True, null=True)  # Field name made lowercase.
    definition = models.CharField(db_column='Definition', max_length=200, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'managelistcategory'


class Menucosting(models.Model):
    id = models.IntegerField(primary_key=True)
    chargemanagerid = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='ChargeManagerID', blank=True, null=True)  # Field name made lowercase.
    itemid = models.ForeignKey(Iteminventory, models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    unitcost = models.DecimalField(db_column='UnitCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    qty = models.IntegerField(db_column='Qty', blank=True, null=True)  # Field name made lowercase.
    finalcost = models.DecimalField(db_column='finalCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    recipeunit = models.ForeignKey(Managelist, models.DO_NOTHING, db_column='recipeUnit', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'menucosting'


class Menuitemcategories(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    parentcategory = models.IntegerField(db_column='ParentCategory', blank=True, null=True)  # Field name made lowercase.
    icon = models.TextField(db_column='Icon', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'menuitemcategories'


class Menuitemmodifiercategory(models.Model):
    id = models.IntegerField(primary_key=True)
    modifiername = models.CharField(db_column='ModifierName', max_length=200, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'menuitemmodifiercategory'


class Menuitemmodifiers(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(max_length=200, blank=True, null=True)
    price = models.CharField(db_column='Price', max_length=50, blank=True, null=True)  # Field name made lowercase.
    modifiercategoryid = models.IntegerField(db_column='ModifierCategoryID', blank=True, null=True)  # Field name made lowercase.
    usepriceschedule = models.CharField(db_column='UsePriceSchedule', max_length=50, blank=True, null=True)  # Field name made lowercase.
    itemcode = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'menuitemmodifiers'


class Messagetemplate(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=500)  # Field name made lowercase.
    message_content = models.CharField(db_column='Message Content', max_length=1000, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    category = models.CharField(db_column='Category', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'messagetemplate'


class Monthlyallowance(models.Model):
    id = models.IntegerField()
    staffid = models.OneToOneField('Staffs', models.DO_NOTHING, db_column='staffID', primary_key=True)  # Field name made lowercase.
    monthlyallowance = models.CharField(db_column='monthlyAllowance', max_length=50, blank=True, null=True)  # Field name made lowercase.
    amount = models.DecimalField(db_column='Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'monthlyallowance'


class Offices(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=50, blank=True, null=True)  # Field name made lowercase.
    block = models.CharField(db_column='Block', max_length=50, blank=True, null=True)  # Field name made lowercase.
    floor = models.CharField(db_column='Floor', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'offices'


class Orders(models.Model):
    id = models.IntegerField(primary_key=True)
    clientid = models.IntegerField(db_column='ClientID', blank=True, null=True)  # Field name made lowercase.
    ordercode = models.CharField(db_column='OrderCode', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    deliveryaddress = models.CharField(db_column='DeliveryAddress', max_length=500, blank=True, null=True)  # Field name made lowercase.
    starttime = models.DateTimeField(db_column='StartTime', blank=True, null=True)  # Field name made lowercase.
    servtime = models.DateTimeField(db_column='ServTime', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    discount = models.IntegerField(db_column='Discount', blank=True, null=True)  # Field name made lowercase.
    discountamount = models.CharField(db_column='DiscountAmount', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    subtotal = models.CharField(db_column='SubTotal', max_length=50, blank=True, null=True)  # Field name made lowercase.
    salestax = models.CharField(db_column='SalesTax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    servicecharge = models.CharField(db_column='ServiceCharge', max_length=50, blank=True, null=True)  # Field name made lowercase.
    total = models.CharField(db_column='Total', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'orders'


class Paymentmethod(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(db_column='Name', max_length=200, blank=True, null=True)  # Field name made lowercase.
    media_type = models.CharField(db_column='Media Type', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    count_media = models.CharField(db_column='Count Media', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    fiscal_code = models.CharField(db_column='Fiscal Code', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    notes = models.CharField(db_column='Notes', max_length=500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'paymentmethod'


class Porders(models.Model):
    id = models.IntegerField(primary_key=True)
    itemname = models.IntegerField(db_column='ItemName', blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)  # Field name made lowercase.
    cost = models.CharField(db_column='Cost', max_length=50)  # Field name made lowercase.
    totalcost = models.CharField(db_column='TotalCost', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'porders'


class Priceschedule(models.Model):
    id = models.IntegerField(primary_key=True)
    itemid = models.IntegerField(db_column='ItemID', blank=True, null=True)  # Field name made lowercase.
    price = models.CharField(db_column='Price', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fromdate = models.DateTimeField(db_column='FromDate', blank=True, null=True)  # Field name made lowercase.
    todate = models.DateTimeField(db_column='ToDate', blank=True, null=True)  # Field name made lowercase.
    fromtime = models.DateTimeField(db_column='FromTime', blank=True, null=True)  # Field name made lowercase.
    totime = models.DateTimeField(db_column='ToTime', blank=True, null=True)  # Field name made lowercase.
    daysofweek = models.CharField(db_column='DaysOfWeek', max_length=50, blank=True, null=True)  # Field name made lowercase.
    sunday = models.CharField(db_column='Sunday', max_length=50, blank=True, null=True)  # Field name made lowercase.
    monday = models.CharField(db_column='Monday', max_length=50, blank=True, null=True)  # Field name made lowercase.
    tuesday = models.CharField(db_column='Tuesday', max_length=50, blank=True, null=True)  # Field name made lowercase.
    wednesday = models.CharField(db_column='Wednesday', max_length=50, blank=True, null=True)  # Field name made lowercase.
    thursday = models.CharField(db_column='Thursday', max_length=50, blank=True, null=True)  # Field name made lowercase.
    friday = models.CharField(db_column='Friday', max_length=50, blank=True, null=True)  # Field name made lowercase.
    saturday = models.CharField(db_column='Saturday', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'priceschedule'


class Priviledges(models.Model):
    id = models.IntegerField(primary_key=True)
    roleid = models.IntegerField(db_column='RoleID', blank=True, null=True)  # Field name made lowercase.
    priviledge = models.IntegerField(db_column='Priviledge')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'priviledges'


class Purchasecharges(models.Model):
    id = models.IntegerField(primary_key=True)
    itemcode = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='ItemCode', blank=True, null=True)  # Field name made lowercase.
    chargedate = models.DateTimeField(db_column='ChargeDate', blank=True, null=True)  # Field name made lowercase.
    chargetime = models.CharField(db_column='ChargeTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    unitcharge = models.DecimalField(db_column='UnitCharge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    unit = models.IntegerField(db_column='Unit', blank=True, null=True)  # Field name made lowercase.
    total = models.DecimalField(db_column='Total', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    finalpayable = models.DecimalField(db_column='FinalPayable', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    purchase_no = models.ForeignKey('Purchases', models.DO_NOTHING, db_column='Purchase No', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.

    class Meta:
        managed = False
        db_table = 'purchasecharges'


class Purchaseorder(models.Model):
    id = models.IntegerField(primary_key=True)
    code = models.CharField(db_column='Code', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    vendorid = models.IntegerField(db_column='VendorID', blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.
    subtotal = models.CharField(db_column='SubTotal', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount = models.CharField(db_column='Discount', max_length=50, blank=True, null=True)  # Field name made lowercase.
    freight = models.CharField(db_column='Freight', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fee = models.CharField(db_column='Fee', max_length=50, blank=True, null=True)  # Field name made lowercase.
    total = models.CharField(db_column='Total', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'purchaseorder'


class Purchases(models.Model):
    purchase_no = models.IntegerField(db_column='Purchase No', primary_key=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    purchase_date = models.DateTimeField(db_column='Purchase Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    purchase_time = models.CharField(db_column='Purchase Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    purchase_category = models.CharField(db_column='Purchase Category', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.
    final_purchase = models.DecimalField(db_column='Final Purchase', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    total_charge = models.DecimalField(db_column='Total Charge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    tax_amount = models.DecimalField(db_column='Tax Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount_amount = models.DecimalField(db_column='Discount Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    expenseid = models.ForeignKey(Expenses, models.DO_NOTHING, db_column='ExpenseID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'purchases'


class Purchasesstore(models.Model):
    id = models.IntegerField(primary_key=True)
    purchase_date = models.DateTimeField(db_column='Purchase Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    purchase_time = models.CharField(db_column='Purchase Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    purchase_category = models.CharField(db_column='Purchase Category', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.
    final_purchase = models.DecimalField(db_column='Final Purchase', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    total_charge = models.DecimalField(db_column='Total Charge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    tax_amount = models.DecimalField(db_column='Tax Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount_amount = models.DecimalField(db_column='Discount Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    expenseid = models.ForeignKey(Expenses, models.DO_NOTHING, db_column='ExpenseID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'purchasesstore'


class Purchasestorecharges(models.Model):
    id = models.IntegerField(primary_key=True)
    itemcode = models.IntegerField(db_column='ItemCode', blank=True, null=True)  # Field name made lowercase.
    chargedate = models.DateTimeField(db_column='ChargeDate', blank=True, null=True)  # Field name made lowercase.
    chargetime = models.CharField(db_column='ChargeTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    unitcharge = models.DecimalField(db_column='UnitCharge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    unit = models.IntegerField(db_column='Unit', blank=True, null=True)  # Field name made lowercase.
    total = models.DecimalField(db_column='Total', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    finalpayable = models.DecimalField(db_column='FinalPayable', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    purchasestoreno = models.ForeignKey(Purchasesstore, models.DO_NOTHING, db_column='PurchaseStoreNo', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'purchasestorecharges'


class Rawmaterialwastelog(models.Model):
    id = models.IntegerField(primary_key=True)
    wastemanagerid = models.ForeignKey('Rawmaterialwastelogmanager', models.DO_NOTHING, db_column='WasteManagerID', blank=True, null=True)  # Field name made lowercase.
    itemid = models.ForeignKey(Iteminventory, models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    unitcost = models.DecimalField(db_column='unitCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    qty = models.IntegerField(blank=True, null=True)
    total = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rawmaterialwastelog'


class Rawmaterialwastelogmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    purchasecategory = models.CharField(db_column='purchaseCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    wasteno = models.CharField(db_column='wasteNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    staffresponsible = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='staffResponsible', blank=True, null=True)  # Field name made lowercase.
    department = models.ForeignKey(Departments, models.DO_NOTHING, db_column='department', blank=True, null=True)
    totalcost = models.DecimalField(db_column='totalCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    wastereason = models.CharField(db_column='wasteReason', max_length=2000, blank=True, null=True)  # Field name made lowercase.
    createdby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='createdBy', related_name='rawmaterialwastelogmanager_createdby_set', blank=True, null=True)  # Field name made lowercase.
    trialid = models.ForeignKey('Trialbalance', models.DO_NOTHING, db_column='trialID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'rawmaterialwastelogmanager'


class Recievingvoucher(models.Model):
    id = models.IntegerField(primary_key=True)
    code = models.CharField(db_column='Code', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    vendorid = models.IntegerField(db_column='VendorID', blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=50, blank=True, null=True)  # Field name made lowercase.
    subtotal = models.CharField(db_column='SubTotal', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount = models.CharField(db_column='Discount', max_length=50, blank=True, null=True)  # Field name made lowercase.
    freight = models.CharField(db_column='Freight', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fee = models.CharField(db_column='Fee', max_length=50, blank=True, null=True)  # Field name made lowercase.
    total = models.CharField(db_column='Total', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'recievingvoucher'


class Refunds(models.Model):
    id = models.IntegerField(primary_key=True)
    billno = models.ForeignKey(Bill, models.DO_NOTHING, db_column='BillNo', blank=True, null=True)  # Field name made lowercase.
    refund_date = models.DateTimeField(db_column='Refund Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    amount = models.DecimalField(db_column='Amount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    payment_mode = models.CharField(db_column='Payment Mode', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    created_by = models.IntegerField(db_column='Created By', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=2000, blank=True, null=True)  # Field name made lowercase.
    refundtime = models.CharField(db_column='RefundTime', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'refunds'


class Reporttable(models.Model):
    id = models.IntegerField(primary_key=True)
    listcategory = models.CharField(db_column='ListCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    category = models.CharField(db_column='Category', max_length=50, blank=True, null=True)  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=100, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'reporttable'


class Requisitionitems(models.Model):
    id = models.IntegerField(primary_key=True)
    requisitionmanagerid = models.ForeignKey('Requisitionmanager', models.DO_NOTHING, db_column='RequisitionManagerID', blank=True, null=True)  # Field name made lowercase.
    itemid = models.ForeignKey(Iteminventory, models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    unitcost = models.DecimalField(db_column='unitCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    qty = models.IntegerField(blank=True, null=True)
    total = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    portionmeasurementunit = models.CharField(db_column='portionMeasurementUnit', max_length=50, blank=True, null=True)  # Field name made lowercase.
    portionstockqty = models.IntegerField(db_column='portionStockQty', blank=True, null=True)  # Field name made lowercase.
    priceforportion = models.DecimalField(db_column='PriceForPortion', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    expirydate = models.DateTimeField(db_column='expiryDate', blank=True, null=True)  # Field name made lowercase.
    stockqty = models.IntegerField(db_column='stockQty', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'requisitionitems'


class Requisitionmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    purchasecategory = models.CharField(db_column='purchaseCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    reqno = models.CharField(db_column='reqNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    requestedby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='requestedBy', blank=True, null=True)  # Field name made lowercase.
    department = models.ForeignKey(Departments, models.DO_NOTHING, db_column='department', blank=True, null=True)
    totalcost = models.DecimalField(db_column='totalCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    comments = models.CharField(max_length=1000, blank=True, null=True)
    createdby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='createdBy', related_name='requisitionmanager_createdby_set', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'requisitionmanager'


class Returnmaterialrequisitionmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    purchasecategory = models.CharField(db_column='purchaseCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    reqno = models.CharField(db_column='reqNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    requestedby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='requestedBy', blank=True, null=True)  # Field name made lowercase.
    department = models.ForeignKey(Departments, models.DO_NOTHING, db_column='department', blank=True, null=True)
    totalcost = models.DecimalField(db_column='totalCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    comments = models.CharField(max_length=1000, blank=True, null=True)
    createdby = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='createdBy', related_name='returnmaterialrequisitionmanager_createdby_set', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'returnmaterialrequisitionmanager'


class Returnrequisitionitems(models.Model):
    id = models.IntegerField(primary_key=True)
    returnrequisitionmanagerid = models.ForeignKey(Returnmaterialrequisitionmanager, models.DO_NOTHING, db_column='ReturnRequisitionManagerID', blank=True, null=True)  # Field name made lowercase.
    itemid = models.ForeignKey(Iteminventory, models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    unitcost = models.DecimalField(db_column='unitCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    qty = models.IntegerField(blank=True, null=True)
    total = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    portionmeasurementunit = models.CharField(db_column='portionMeasurementUnit', max_length=50, blank=True, null=True)  # Field name made lowercase.
    portionstockqty = models.IntegerField(db_column='portionStockQty', blank=True, null=True)  # Field name made lowercase.
    priceforportion = models.DecimalField(db_column='PriceForPortion', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    expirydate = models.DateTimeField(db_column='expiryDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'returnrequisitionitems'


class Returnvoucher(models.Model):
    id = models.IntegerField(primary_key=True)
    code = models.CharField(db_column='Code', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    vendorid = models.IntegerField(db_column='VendorID', blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.
    subtotal = models.CharField(db_column='SubTotal', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discount = models.CharField(db_column='Discount', max_length=50, blank=True, null=True)  # Field name made lowercase.
    freight = models.CharField(db_column='Freight', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fee = models.CharField(db_column='Fee', max_length=50, blank=True, null=True)  # Field name made lowercase.
    total = models.CharField(db_column='Total', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'returnvoucher'


class Returnvoucherorder(models.Model):
    id = models.IntegerField(primary_key=True)
    itemname = models.IntegerField(db_column='ItemName', blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)  # Field name made lowercase.
    cost = models.CharField(db_column='Cost', max_length=50, blank=True, null=True)  # Field name made lowercase.
    totalcost = models.CharField(db_column='TotalCost', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'returnvoucherorder'


class Roomcondition(models.Model):
    id = models.IntegerField(primary_key=True)
    roomcondition = models.CharField(db_column='roomCondition', max_length=50, blank=True, null=True)  # Field name made lowercase.
    notes = models.CharField(max_length=500, blank=True, null=True)
    room = models.ForeignKey('self', models.DO_NOTHING, db_column='room', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'roomcondition'


class Rooms(models.Model):
    id = models.IntegerField(primary_key=True)
    code = models.CharField(max_length=50, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    chargeid = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='chargeID', blank=True, null=True)  # Field name made lowercase.
    cardno = models.CharField(db_column='cardNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    lockno = models.CharField(db_column='lockNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    cardhex = models.CharField(db_column='cardHex', max_length=500, blank=True, null=True)  # Field name made lowercase.
    roomstatus = models.CharField(db_column='RoomStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.
    reservationstatus = models.CharField(db_column='ReservationStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.
    floor = models.IntegerField(blank=True, null=True)
    amorpm = models.CharField(db_column='AmOrPM', max_length=50, blank=True, null=True)  # Field name made lowercase.
    features = models.CharField(max_length=500, blank=True, null=True)
    roomcondition = models.CharField(db_column='roomCondition', max_length=50, blank=True, null=True)  # Field name made lowercase.
    housekeepingpriority = models.CharField(db_column='housekeepingPriority', max_length=50, blank=True, null=True)  # Field name made lowercase.
    reservationarrivaldate = models.DateTimeField(db_column='reservationArrivalDate', blank=True, null=True)  # Field name made lowercase.
    reservationdeparturedate = models.DateTimeField(db_column='reservationDepartureDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'rooms'


class Roomtaxes(models.Model):
    id = models.IntegerField(primary_key=True)
    roomtypeid = models.IntegerField(db_column='RoomTypeID', blank=True, null=True)  # Field name made lowercase.
    tax = models.IntegerField(db_column='Tax', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'roomtaxes'


class Roomtypes(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(db_column='Name', max_length=200, blank=True, null=True)  # Field name made lowercase.
    adultpersons = models.CharField(db_column='AdultPersons', max_length=50, blank=True, null=True)  # Field name made lowercase.
    chargeperiod = models.CharField(db_column='ChargePeriod', max_length=50, blank=True, null=True)  # Field name made lowercase.
    priceperday = models.CharField(db_column='PricePerDay', max_length=50, blank=True, null=True)  # Field name made lowercase.
    taxes = models.CharField(db_column='Taxes', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'roomtypes'


class Salarysheet(models.Model):
    id = models.IntegerField(primary_key=True)
    staffid = models.ForeignKey('Staffs', models.DO_NOTHING, db_column='staffID', blank=True, null=True)  # Field name made lowercase.
    grosssalary = models.DecimalField(db_column='grossSalary', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    taxpercent = models.CharField(db_column='taxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    taxamount = models.DecimalField(db_column='taxAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    pensionpercent = models.CharField(db_column='PensionPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    pensionamount = models.DecimalField(db_column='PensionAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    inssurancepercent = models.CharField(db_column='InssurancePercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    inssuranceamount = models.DecimalField(db_column='InssuranceAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    netsalary = models.DecimalField(db_column='NetSalary', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    datemonth = models.CharField(db_column='dateMonth', max_length=50, blank=True, null=True)  # Field name made lowercase.
    expenseid = models.ForeignKey(Expenses, models.DO_NOTHING, db_column='ExpenseID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'salarysheet'


class Securityroles(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(db_column='Name', max_length=200, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'securityroles'


class Serviceprice(models.Model):
    id = models.IntegerField(primary_key=True)
    serviceid = models.IntegerField(db_column='serviceID', blank=True, null=True)  # Field name made lowercase.
    price = models.CharField(db_column='Price', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'serviceprice'


class Services(models.Model):
    id = models.IntegerField(primary_key=True)
    servicename = models.CharField(db_column='Servicename', max_length=200, blank=True, null=True)  # Field name made lowercase.
    shortname = models.CharField(max_length=50, blank=True, null=True)
    comment = models.CharField(max_length=1000, blank=True, null=True)
    primaryservice = models.CharField(db_column='primaryService', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'services'


class Sessions(models.Model):
    id = models.IntegerField(primary_key=True)
    usercode = models.ForeignKey('Users', models.DO_NOTHING, db_column='UserCode', blank=True, null=True)  # Field name made lowercase.
    logindate = models.DateTimeField(db_column='LoginDate', blank=True, null=True)  # Field name made lowercase.
    logintime = models.CharField(db_column='LoginTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    logoutdate = models.DateTimeField(db_column='LogoutDate', blank=True, null=True)  # Field name made lowercase.
    logouttime = models.CharField(db_column='LogoutTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    computername = models.CharField(db_column='ComputerName', max_length=500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'sessions'


class Shifts(models.Model):
    id = models.IntegerField(primary_key=True)
    employeeid = models.IntegerField(db_column='employeeID', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    start_time = models.CharField(db_column='Start Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    finish_time = models.CharField(db_column='Finish Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=500, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'shifts'


class Specialdays(models.Model):
    code = models.IntegerField(db_column='Code', primary_key=True)  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=50, blank=True, null=True)  # Field name made lowercase.
    month = models.CharField(db_column='Month', max_length=50, blank=True, null=True)  # Field name made lowercase.
    monthno = models.CharField(db_column='MonthNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    day = models.CharField(db_column='Day', max_length=50, blank=True, null=True)  # Field name made lowercase.
    is_appointment_on_day = models.CharField(db_column='Is Appointment On Day', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    reminder_on_day = models.CharField(db_column='Reminder on Day', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.

    class Meta:
        managed = False
        db_table = 'specialdays'


class Staffs(models.Model):
    id = models.IntegerField(primary_key=True)
    firstname = models.CharField(db_column='FirstName', max_length=200, blank=True, null=True)  # Field name made lowercase.
    lastname = models.CharField(db_column='LastName', max_length=200, blank=True, null=True)  # Field name made lowercase.
    sex = models.CharField(db_column='Sex', max_length=50, blank=True, null=True)  # Field name made lowercase.
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)  # Field name made lowercase.
    dateofbirth = models.DateTimeField(db_column='DateOfBirth', blank=True, null=True)  # Field name made lowercase.
    maritalstatus = models.CharField(db_column='MaritalStatus', max_length=100, blank=True, null=True)  # Field name made lowercase.
    spousename = models.CharField(db_column='SpouseName', max_length=200, blank=True, null=True)  # Field name made lowercase.
    children = models.CharField(db_column='Children', max_length=50, blank=True, null=True)  # Field name made lowercase.
    qualification = models.CharField(db_column='Qualification', max_length=200, blank=True, null=True)  # Field name made lowercase.
    contactmethod = models.CharField(db_column='ContactMethod', max_length=100, blank=True, null=True)  # Field name made lowercase.
    phone = models.CharField(db_column='Phone', max_length=50, blank=True, null=True)  # Field name made lowercase.
    mobile = models.CharField(db_column='Mobile', max_length=50, blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=50, blank=True, null=True)  # Field name made lowercase.
    country = models.CharField(db_column='Country', max_length=50, blank=True, null=True)  # Field name made lowercase.
    zip = models.CharField(db_column='Zip', max_length=50, blank=True, null=True)  # Field name made lowercase.
    state = models.CharField(db_column='State', max_length=100, blank=True, null=True)  # Field name made lowercase.
    city = models.CharField(db_column='City', max_length=100, blank=True, null=True)  # Field name made lowercase.
    employmentdate = models.DateTimeField(db_column='EmploymentDate', blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    tipsrate = models.CharField(db_column='TipsRate', max_length=50, blank=True, null=True)  # Field name made lowercase.
    position = models.CharField(db_column='Position', max_length=200, blank=True, null=True)  # Field name made lowercase.
    barcode = models.CharField(db_column='Barcode', max_length=200, blank=True, null=True)  # Field name made lowercase.
    notes = models.CharField(db_column='Notes', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    accountnumber = models.CharField(db_column='AccountNumber', max_length=50, blank=True, null=True)  # Field name made lowercase.
    accountname = models.CharField(db_column='AccountName', max_length=500, blank=True, null=True)  # Field name made lowercase.
    accounttype = models.CharField(db_column='AccountType', max_length=200, blank=True, null=True)  # Field name made lowercase.
    bank = models.CharField(db_column='Bank', max_length=500, blank=True, null=True)  # Field name made lowercase.
    bankaddress = models.CharField(db_column='BankAddress', max_length=1000, blank=True, null=True)  # Field name made lowercase.
    monthlyloandeduction = models.CharField(db_column='MonthlyLoanDeduction', max_length=50, blank=True, null=True)  # Field name made lowercase.
    totalpay = models.CharField(db_column='TotalPay', max_length=50, blank=True, null=True)  # Field name made lowercase.
    remark = models.CharField(db_column='Remark', max_length=500, blank=True, null=True)  # Field name made lowercase.
    fullname = models.CharField(db_column='FullName', max_length=500, blank=True, null=True)  # Field name made lowercase.
    picture = models.TextField(db_column='Picture', blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    inactivedate = models.DateTimeField(db_column='InactiveDate', blank=True, null=True)  # Field name made lowercase.
    pensiondeduction = models.CharField(db_column='pensionDeduction', max_length=50, blank=True, null=True)  # Field name made lowercase.
    departmentid = models.ForeignKey(Departments, models.DO_NOTHING, db_column='departmentID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'staffs'


class Stays(models.Model):
    id = models.IntegerField(primary_key=True)
    clientid = models.ForeignKey(Clients, models.DO_NOTHING, db_column='ClientID', blank=True, null=True)  # Field name made lowercase.
    checkingdate = models.DateTimeField(db_column='CheckingDate', blank=True, null=True)  # Field name made lowercase.
    checkingtime = models.CharField(db_column='CheckingTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    checkedinby = models.ForeignKey(Staffs, models.DO_NOTHING, db_column='CheckedInBy', blank=True, null=True)  # Field name made lowercase.
    totalnights = models.IntegerField(db_column='TotalNights', blank=True, null=True)  # Field name made lowercase.
    checkoutdate = models.DateTimeField(db_column='CheckoutDate', blank=True, null=True)  # Field name made lowercase.
    checkouttime = models.CharField(db_column='CheckoutTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    checkedoutby = models.ForeignKey(Staffs, models.DO_NOTHING, db_column='CheckedOutBy', related_name='stays_checkedoutby_set', blank=True, null=True)  # Field name made lowercase.
    checkoutnote = models.CharField(db_column='CheckOutNote', max_length=500, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    reserveddatefrom = models.DateTimeField(db_column='ReservedDateFrom', blank=True, null=True)  # Field name made lowercase.
    reserveddateto = models.DateTimeField(db_column='ReservedDateTo', blank=True, null=True)  # Field name made lowercase.
    notes = models.CharField(db_column='Notes', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    room = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='Room', blank=True, null=True)  # Field name made lowercase.
    adult = models.CharField(db_column='Adult', max_length=50, blank=True, null=True)  # Field name made lowercase.
    child = models.CharField(db_column='Child', max_length=50, blank=True, null=True)  # Field name made lowercase.
    discountpercent = models.CharField(db_column='DiscountPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.
    sendstatus = models.CharField(db_column='sendStatus', max_length=50, blank=True, null=True)  # Field name made lowercase.
    timearrival = models.CharField(db_column='timeArrival', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'stays'


class Stockportionunits(models.Model):
    id = models.IntegerField(primary_key=True)
    itemid = models.ForeignKey(Iteminventory, models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    qty = models.BigIntegerField(blank=True, null=True)
    unitinmeasurement = models.BigIntegerField(db_column='unitInMeasurement', blank=True, null=True)  # Field name made lowercase.
    priceperunit = models.DecimalField(db_column='pricePerUnit', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    description = models.CharField(max_length=1000, blank=True, null=True)
    recipemeasurementunit = models.ForeignKey(Managelist, models.DO_NOTHING, db_column='RecipeMeasurementUnit', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'stockportionunits'


class Stockstore(models.Model):
    id = models.IntegerField(primary_key=True)
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    purchasecategory = models.CharField(db_column='purchaseCategory', max_length=50, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=1000, blank=True, null=True)  # Field name made lowercase.
    totalcharge = models.DecimalField(db_column='totalCharge', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    expenseid = models.ForeignKey(Expenses, models.DO_NOTHING, db_column='expenseID', blank=True, null=True)  # Field name made lowercase.
    voucherno = models.CharField(db_column='voucherNo', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'stockstore'


class Stockstoreitems(models.Model):
    id = models.IntegerField(primary_key=True)
    stockstoreid = models.ForeignKey(Stockstore, models.DO_NOTHING, db_column='stockStoreID', blank=True, null=True)  # Field name made lowercase.
    itemid = models.ForeignKey(Iteminventory, models.DO_NOTHING, db_column='itemID', blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(blank=True, null=True)
    time = models.CharField(max_length=50, blank=True, null=True)
    unitcost = models.DecimalField(db_column='unitCost', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    qty = models.IntegerField(blank=True, null=True)
    total = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    expirydate = models.DateTimeField(db_column='expiryDate', blank=True, null=True)  # Field name made lowercase.
    portionmeasurementunit = models.CharField(db_column='portionMeasurementUnit', max_length=50, blank=True, null=True)  # Field name made lowercase.
    portionstockqty = models.IntegerField(db_column='portionStockQty', blank=True, null=True)  # Field name made lowercase.
    priceforportion = models.DecimalField(db_column='PriceForPortion', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'stockstoreitems'


class Sysdiagrams(models.Model):
    name = models.CharField(max_length=160)
    principal_id = models.IntegerField()
    diagram_id = models.IntegerField(primary_key=True)
    version = models.IntegerField(blank=True, null=True)
    definition = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'sysdiagrams'
        unique_together = (('principal_id', 'name'),)


class Taxation(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    taxpercent = models.CharField(db_column='TaxPercent', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'taxation'


class Testtable(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    comment = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'testtable'


class Titles(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=200, blank=True, null=True)  # Field name made lowercase.
    genderfortitle = models.CharField(db_column='GenderForTitle', max_length=200, blank=True, null=True)  # Field name made lowercase.
    maritalstatusfortitle = models.IntegerField(db_column='MaritalStatusForTitle', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'titles'


class Todolist(models.Model):
    id = models.IntegerField(primary_key=True)
    subject = models.CharField(db_column='Subject', max_length=50, blank=True, null=True)  # Field name made lowercase.
    due_date = models.CharField(db_column='Due Date', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    priority = models.CharField(db_column='Priority', max_length=50, blank=True, null=True)  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=50, blank=True, null=True)  # Field name made lowercase.
    reminder_frequency = models.CharField(db_column='Reminder Frequency', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    employeeid = models.IntegerField(db_column='EmployeeID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'todolist'


class Transactions(models.Model):
    transaction_code = models.IntegerField(db_column='Transaction Code', primary_key=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    transaction_date = models.DateTimeField(db_column='Transaction Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    transaction_type = models.CharField(db_column='Transaction Type', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    sub_type = models.CharField(db_column='Sub Type', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    payment_mode = models.CharField(db_column='Payment Mode', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    compesation_amount = models.CharField(db_column='Compesation Amount', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    category = models.CharField(db_column='Category', max_length=50, blank=True, null=True)  # Field name made lowercase.
    created_by = models.IntegerField(db_column='Created By', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    comment = models.CharField(db_column='Comment', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    totalamount = models.DecimalField(db_column='TotalAmount', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    billno = models.ForeignKey(Bill, models.DO_NOTHING, db_column='BillNo', blank=True, null=True)  # Field name made lowercase.
    transactiontime = models.CharField(db_column='TransactionTime', max_length=50, blank=True, null=True)  # Field name made lowercase.
    changes = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    amountdeposit = models.DecimalField(db_column='AmountDeposit', max_digits=19, decimal_places=4, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'transactions'


class Transportation(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(db_column='Description', max_length=50)  # Field name made lowercase.
    registration_no = models.CharField(db_column='Registration No', max_length=50)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    chargeid = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='chargeID', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'transportation'


class Transportrequest(models.Model):
    id = models.IntegerField(db_column='ID', primary_key=True)  # Field name made lowercase.
    requesting_date = models.DateTimeField(db_column='Requesting Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    requesting_person = models.CharField(db_column='Requesting Person', max_length=500, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    phone = models.CharField(db_column='Phone', max_length=50, blank=True, null=True)  # Field name made lowercase.
    mobile = models.CharField(db_column='Mobile', max_length=50, blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)  # Field name made lowercase.
    requesting_type = models.CharField(db_column='Requesting Type', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    weight = models.CharField(db_column='Weight', max_length=50, blank=True, null=True)  # Field name made lowercase.
    fromdestination = models.CharField(db_column='FromDestination', max_length=500, blank=True, null=True)  # Field name made lowercase.
    todestination = models.CharField(db_column='ToDestination', max_length=500, blank=True, null=True)  # Field name made lowercase.
    charge_for_destination = models.CharField(db_column='Charge For Destination', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    requirement = models.CharField(db_column='Requirement', max_length=200, blank=True, null=True)  # Field name made lowercase.
    dispatched_date = models.DateTimeField(db_column='Dispatched Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    pick_up_date = models.DateTimeField(db_column='Pick Up Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    delivery_date = models.DateTimeField(db_column='Delivery Date', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    transport_mode = models.ForeignKey(Chargemanager, models.DO_NOTHING, db_column='Transport Mode', blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    distance = models.CharField(db_column='Distance', max_length=50, blank=True, null=True)  # Field name made lowercase.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    driverassignedid = models.ForeignKey(Staffs, models.DO_NOTHING, db_column='DriverAssignedID', blank=True, null=True)  # Field name made lowercase.
    billno = models.ForeignKey(Bill, models.DO_NOTHING, db_column='BillNo', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'transportrequest'


class Trialbalance(models.Model):
    id = models.IntegerField(primary_key=True)
    amount = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    accountheadid = models.IntegerField(db_column='accountHeadID', blank=True, null=True)  # Field name made lowercase.
    category = models.CharField(db_column='Category', max_length=50, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    time = models.CharField(db_column='Time', max_length=50, blank=True, null=True)  # Field name made lowercase.
    cashbank = models.IntegerField(db_column='CashBank', blank=True, null=True)  # Field name made lowercase.
    tax = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    discount = models.DecimalField(max_digits=19, decimal_places=4, blank=True, null=True)
    status = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'trialbalance'


class Users(models.Model):
    id = models.IntegerField(primary_key=True)
    username = models.CharField(max_length=50, blank=True, null=True)
    password = models.CharField(max_length=50, blank=True, null=True)
    staffid = models.ForeignKey(Staffs, models.DO_NOTHING, db_column='staffID', blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=5000, blank=True, null=True)  # Field name made lowercase.
    usertype = models.IntegerField(db_column='userType', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'users'


class Userslistboxaccesstable(models.Model):
    id = models.IntegerField(primary_key=True)
    item_category = models.CharField(db_column='Item Category', max_length=200, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    item = models.CharField(db_column='Item', max_length=200, blank=True, null=True)  # Field name made lowercase.
    hasaccesstoform = models.CharField(db_column='HasAccessToForm', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'userslistboxaccesstable'


class Vendors(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(db_column='Name', max_length=500, blank=True, null=True)  # Field name made lowercase.
    sex = models.CharField(db_column='Sex', max_length=50, blank=True, null=True)  # Field name made lowercase.
    phone = models.CharField(max_length=50, blank=True, null=True)
    mobile = models.CharField(db_column='Mobile', max_length=50, blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)  # Field name made lowercase.
    jobtitle = models.CharField(db_column='JobTitle', max_length=200, blank=True, null=True)  # Field name made lowercase.
    city = models.CharField(db_column='City', max_length=200, blank=True, null=True)  # Field name made lowercase.
    state = models.CharField(db_column='State', max_length=200, blank=True, null=True)  # Field name made lowercase.
    zip = models.CharField(db_column='Zip', max_length=50, blank=True, null=True)  # Field name made lowercase.
    country = models.CharField(db_column='Country', max_length=200, blank=True, null=True)  # Field name made lowercase.
    fax = models.CharField(db_column='Fax', max_length=50, blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=50, blank=True, null=True)  # Field name made lowercase.
    company = models.CharField(db_column='Company', max_length=500, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    systemused = models.CharField(db_column='SystemUsed', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'vendors'


class Visitormanager(models.Model):
    id = models.IntegerField(db_column='ID', primary_key=True)  # Field name made lowercase.
    visitorname = models.CharField(db_column='VisitorName', max_length=200, blank=True, null=True)  # Field name made lowercase.
    date = models.DateTimeField(db_column='Date', blank=True, null=True)  # Field name made lowercase.
    time = models.CharField(db_column='Time', max_length=50, blank=True, null=True)  # Field name made lowercase.
    visitedlocation = models.ForeignKey(Stays, models.DO_NOTHING, db_column='VisitedLocation', blank=True, null=True)  # Field name made lowercase.
    visitpurpose = models.CharField(db_column='VisitPurpose', max_length=5000, blank=True, null=True)  # Field name made lowercase.
    mobilenumber = models.CharField(db_column='MobileNumber', max_length=50, blank=True, null=True)  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=1500, blank=True, null=True)  # Field name made lowercase.
    others = models.CharField(db_column='Others', max_length=50, blank=True, null=True)  # Field name made lowercase.
    out_time = models.CharField(db_column='Out Time', max_length=50, blank=True, null=True)  # Field name made lowercase. Field renamed to remove unsuitable characters.
    status = models.CharField(db_column='Status', max_length=50, blank=True, null=True)  # Field name made lowercase.
    comment = models.CharField(db_column='Comment', max_length=5000, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'visitormanager'


class Voucherorder(models.Model):
    id = models.IntegerField(primary_key=True)
    itemname = models.IntegerField(db_column='ItemName', blank=True, null=True)  # Field name made lowercase.
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)  # Field name made lowercase.
    cost = models.CharField(db_column='Cost', max_length=50, blank=True, null=True)  # Field name made lowercase.
    totalcost = models.CharField(db_column='TotalCost', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'voucherorder'


class Windowmanager(models.Model):
    id = models.IntegerField(primary_key=True)
    window = models.CharField(db_column='Window', max_length=50, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'windowmanager'


class Windows(models.Model):
    id = models.IntegerField(primary_key=True)
    userid = models.ForeignKey(Users, models.DO_NOTHING, db_column='UserID', blank=True, null=True)  # Field name made lowercase.
    window = models.CharField(db_column='Window', max_length=50, blank=True, null=True)  # Field name made lowercase.
    view = models.IntegerField(db_column='View', blank=True, null=True)  # Field name made lowercase.
    add = models.IntegerField(db_column='Add', blank=True, null=True)  # Field name made lowercase.
    edit = models.IntegerField(db_column='Edit', blank=True, null=True)  # Field name made lowercase.
    delete = models.IntegerField(db_column='Delete', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'windows'
